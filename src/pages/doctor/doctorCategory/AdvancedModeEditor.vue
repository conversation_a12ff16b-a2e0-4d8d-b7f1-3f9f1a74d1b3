<template>
    <div class="advanced-mode-container">
        <div class="advanced-header">
            <h3>自定义条件</h3>
            <i-button
                type="primary"
                size="small"
                @click="loadFromBasic"
            >
                载入字典
            </i-button>
        </div>
        <i-input
            v-model="localCondition"
            type="textarea"
            :rows="20"
            placeholder="请输入自定义条件..."
            class="advanced-textarea"
            @input="handleInput"
        />
        <div class="advanced-footer">
            <div class="predicted-count">
                预计人数（不含自定义uid）：{{ predictedCount }}
                <a @click="refreshCount">刷新</a>
            </div>
            <i-button
                v-if="!disabled"
                type="primary"
                @click="handleSubmit"
            >
                保存
            </i-button>
            <i-button
                v-else
                type="primary"
                disabled
            >
                仅可查看，不可修改
            </i-button>
        </div>
    </div>
</template>

<script lang="tsx" setup>
import { ref, watch, computed } from 'vue';
import { DoctorCategoryData } from './type';

interface Props {
    data?: DoctorCategoryData;
    disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    disabled: false,
});

const emit = defineEmits<{
    submit: [data: any];
    'update:data': [data: DoctorCategoryData];
}>();

const localCondition = ref('');
const predictedCount = ref(0);

// 监听外部数据变化
watch(() => props.data, newData => {
    if (newData && Object.keys(newData).length > 0) {
        localCondition.value = JSON.stringify(newData, null, 2);
    }
}, { immediate: true });

const handleInput = () => {
    // 可以在这里添加实时验证逻辑
};

const loadFromBasic = () => {
    // 从基础模式加载条件到高级模式
    const basicTemplate = {
        'user_range': '1',
        'user_regist_type': '1',
        'user_type': ['其他'],
        'auth_type': ['信誉认证通过'],
        'forbidden_end': ['未封禁'],
        'acc_delete_status': ['未注销'],
        'company_grade': ['未知'],
        'city_range': [],
        'hospitals': [],
        'clinical_title': [],
        'std_practice_depts': [],
        'cid2': [],
        'rights_str': {
            'include': [],
            'exclude': [],
        },
    };

    localCondition.value = JSON.stringify(basicTemplate, null, 2);
};

const refreshCount = async () => {
    // 这里可以调用API获取真实的预计人数
    try {
        const parsedCondition = JSON.parse(localCondition.value);
        // 模拟API调用
        predictedCount.value = Math.floor(Math.random() * 10000);

        // 实际项目中可以这样调用：
        // const response = await getPreTotal(parsedCondition);
        // predictedCount.value = response.data.total;
    } catch (error) {
        console.error('JSON格式错误，无法获取预计人数:', error);
        predictedCount.value = 0;
    }
};

const handleSubmit = () => {
    try {
        const parsedCondition = JSON.parse(localCondition.value);
        emit('submit', parsedCondition);
    } catch (error) {
        console.error('JSON格式错误:', error);
        // 这里可以添加错误提示
        // Message.error('JSON格式错误，请检查输入内容');
    }
};

// 验证JSON格式
const isValidJson = computed(() => {
    try {
        JSON.parse(localCondition.value);
        return true;
    } catch {
        return false;
    }
});

// 暴露一些方法给父组件使用
defineExpose({
    refreshCount,
    loadFromBasic,
    isValidJson,
    getCondition: () => localCondition.value,
    setCondition: (condition: string) => {
        localCondition.value = condition;
    },
});
</script>

<style lang="less" scoped>
.advanced-mode-container {
    .advanced-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
        }
    }

    .advanced-textarea {
        margin-bottom: 12px;

        :deep(.ivu-input) {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            line-height: 1.4;

            &.error {
                border-color: #ed4014;
            }
        }
    }

    .advanced-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .predicted-count {
            font-size: 14px;
            color: #666;

            a {
                color: #2d8cf0;
                cursor: pointer;
                text-decoration: none;
                margin-left: 8px;

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
}
</style>
