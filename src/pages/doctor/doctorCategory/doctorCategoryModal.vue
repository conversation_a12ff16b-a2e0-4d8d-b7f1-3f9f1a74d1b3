<template>
    <i-modal
        :value="value"
        footer-hide
        width="600"
        @input="closeModal"
        @on-visible-change="onReset"
    >
        <!-- 自定义 header 插槽 -->
        <template #header>
            <div class="modal-header">
                <span class="modal-title">{{ title }}</span>
                <div class="mode-button-group">
                    <i-button
                        class="mode-button mode-button-left"
                        :type="mode === 'basic' ? 'primary' : 'default'"
                        size="small"
                        @click="switchMode('basic')"
                    >
                        基础模式
                    </i-button>
                    <i-button
                        class="mode-button mode-button-right"
                        :type="mode === 'advanced' ? 'primary' : 'default'"
                        size="small"
                        @click="switchMode('advanced')"
                    >
                        高级模式
                    </i-button>
                </div>
            </div>
        </template>

        <!-- 基础模式 -->
        <doctor-category
            v-if="mode === 'basic'"
            :id="id"
            ref="categoryForm"
            :data="data"
            :max-height="600"
            :disabled="disabled"
            @submit="submit"
        />

        <!-- 高级模式 -->
        <advanced-mode-editor
            v-else-if="mode === 'advanced'"
            ref="advancedEditor"
            :data="data"
            :disabled="disabled"
            @submit="submit"
        />
    </i-modal>
</template>

<script lang="tsx" setup>
import doctorCategory from './doctorCategory.vue';
import AdvancedModeEditor from './AdvancedModeEditor.vue';
import { DoctorCategoryData } from './type';
import { ref } from 'vue';

const props = withDefaults(defineProps<{
    value: boolean;
    title: string;
    id?: string;
    disabled?: boolean,
    data: DoctorCategoryData
}>(), {
    value: false,
    title: '编辑条件',
    id: '',
    disabled: false,
});

const categoryForm = ref();
const advancedEditor = ref();
const mode = ref<'basic' | 'advanced'>('basic');

const emit = defineEmits(['input', 'submit']);

const closeModal = () => {
    emit('input', false);
};

const submit = (params: any) => {
    emit('submit', params);
};

const onReset = (status: boolean) => {
    if (!status) {
        categoryForm.value?.reset();
        mode.value = 'basic';
    }
};

const switchMode = (newMode: 'basic' | 'advanced') => {
    mode.value = newMode;
};
</script>

<style lang="less" scoped>
.mode-switch-container {
    margin-bottom: 16px;
    text-align: center;
    border-bottom: 1px solid #e8eaec;
    padding-bottom: 16px;

    .mode-button-group {
        display: inline-block;

        .mode-button {
            margin: 0;

            &.mode-button-left {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
                border-right: none;
            }

            &.mode-button-right {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
                margin-left: -1px;
            }
        }
    }
}


</style>
