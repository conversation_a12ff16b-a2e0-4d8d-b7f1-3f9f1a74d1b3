<template>
    <div style="padding: 20px;">
        <h2>测试医生分类模态框</h2>

        <div style="margin-bottom: 20px;">
            <h3>完整模态框测试</h3>
            <i-button type="primary" @click="showModal = true">
                打开医生分类模态框
            </i-button>
        </div>

        <div style="margin-bottom: 20px;">
            <h3>高级模式组件单独测试</h3>
            <advanced-mode-editor
                :data="testData"
                @submit="handleAdvancedSubmit"
            />
        </div>

        <doctor-category-modal
            v-model="showModal"
            title="编辑条件"
            :data="testData"
            @submit="handleSubmit"
        />
    </div>
</template>

<script lang="tsx" setup>
import { ref } from 'vue';
import doctorCategoryModal from './doctorCategoryModal.vue';
import AdvancedModeEditor from './AdvancedModeEditor.vue';

const showModal = ref(false);

const testData = ref({
    user_range: "1",
    user_regist_type: "1", 
    user_type: ["其他"],
    auth_type: ["信誉认证通过"],
    forbidden_end: ["未封禁"],
    acc_delete_status: ["未注销"],
    company_grade: ["未知"],
    city_range: [],
    hospitals: [],
    clinical_title: [],
    std_practice_depts: [],
    cid2: [],
    rights_str: {
        include: [],
        exclude: []
    }
});

const handleSubmit = (params: any) => {
    console.log('提交的参数:', params);
    showModal.value = false;
};
</script>
</template>
